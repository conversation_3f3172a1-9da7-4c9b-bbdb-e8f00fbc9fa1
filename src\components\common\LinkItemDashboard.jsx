import { Grid, Paper, Typography, Box, Skeleton } from "@mui/material";
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Client } from "../../api/client";

const LinkItemDashboard = ({ links,Data }) => {
  const [count, setCount] = React.useState(0);

  // Fetch notification counts
  const fetchNotificationCounts = async () => {
    try {
      const response = await Client.get("/user/notification-count");
      if (response.data.success) {
        setCount(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching notification counts:", error);
    }
  };
  React.useEffect(() => {
    fetchNotificationCounts();
  }, []);

  return (
    <Grid container spacing={2} sx={{ mt: 2, mb: 4 }}>
      <Grid item xs={12} md={6}>
        <Grid container spacing={3}>
          {links.length > 0
            ? links.map((card, index) => {
                return (
                  <Grid item xs={12} md={6} key={index}>
                    <Link
                      to={card.link}
                      style={{ textDecoration: "none" }}
                      aria-disabled={card.link === "#"}
                    >
                      <Paper
                        elevation={4}
                        sx={{
                          bgcolor: card.color,
                          p: 2,
                          borderRadius: 3,

                          py: 3,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          cursor: card.link !== "#" ? "pointer" : "not-allowed",
                        }}
                      >
                        <Typography
                          variant="h5"
                          sx={{ ml: 2 }}
                          fontWeight="medium"
                        >
                          {card.title}
                        </Typography>
                        <Box sx={{ mr: 2, position: "relative" }}>
                          {card.icon}
                          {card.title === "Notifications" && count > 0 && (
                            <Box
                              sx={{
                                position: "absolute",
                                top: -8,
                                right: -8,
                                backgroundColor: "error.main",
                                color: "white",
                                borderRadius: "50%",
                                width: 20,
                                height: 20,
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                fontSize: "0.75rem",
                              }}
                            >
                              {count}
                            </Box>
                          )}
                        </Box>
                      </Paper>
                    </Link>
                  </Grid>
                );
              })
            : Array(6)
                .fill(0)
                .map((_, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Skeleton
                      variant="rectangular"
                      width={300}
                      height={80}
                      sx={{ borderRadius: 3 }}
                    />
                  </Grid>
                ))}
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
          <Paper
          sx={{
            maxWidth: 550,
            maxHeight: 400,
            
            overflow: 'hidden',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center', minHeight: 500
          }}
          elevation={4}
        >
          {Data && Data.url ? (
            Data.format === "video" ? (

              <iframe width="100%" height="100%" src={Data.url} title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

            ) : (
              <img
                src={Data.url}
                alt="Ad"
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                  display: "block",
                }}
              />
            )
          ) : (
            <Typography variant="h5">Ad</Typography>
          )}
        </Paper>
      </Grid>
    </Grid>
  );
};

export default LinkItemDashboard;
